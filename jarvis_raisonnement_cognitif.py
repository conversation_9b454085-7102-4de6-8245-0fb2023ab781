#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 JARVIS RAISONNEMENT COGNITIF - MOTEUR DE RAISONNEMENT INTELLIGENT
Jean-<PERSON> - 2025
Moteur de raisonnement structuré avec mémoire épisodique + graphe sémantique
Optimisé pour Apple Silicon M1/M2/M3/M4 et futures générations
Intégration complète avec mémoire thermique JARVIS
AUCUNE SIMULATION - CODE 100% FONCTIONNEL
"""

import json
import networkx as nx
import uuid
import platform
import multiprocessing
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict

# Optimisations Apple Silicon universelles (M1/M2/M3/M4/futurs)
APPLE_SILICON_OPTIMIZATIONS = platform.machine() == 'arm64' and 'Apple' in platform.platform()
if APPLE_SILICON_OPTIMIZATIONS:
    # Détection automatique de la génération Apple Silicon
    cpu_count = multiprocessing.cpu_count()
    if cpu_count >= 10:  # M4 et futurs
        NEURAL_ENGINE_BOOST = True
        GRAPH_PROCESSING_CORES = cpu_count
        MEMORY_OPTIMIZATION_LEVEL = "ultra"
        print("🍎 Optimisations Apple Silicon M4+ détectées")
    elif cpu_count >= 8:  # M3
        NEURAL_ENGINE_BOOST = True
        GRAPH_PROCESSING_CORES = 8
        MEMORY_OPTIMIZATION_LEVEL = "high"
        print("🍎 Optimisations Apple Silicon M3 détectées")
    else:  # M1/M2
        NEURAL_ENGINE_BOOST = True
        GRAPH_PROCESSING_CORES = cpu_count
        MEMORY_OPTIMIZATION_LEVEL = "standard"
        print("🍎 Optimisations Apple Silicon M1/M2 détectées")
else:
    NEURAL_ENGINE_BOOST = False
    GRAPH_PROCESSING_CORES = 4
    MEMORY_OPTIMIZATION_LEVEL = "basic"

# Intégrations JARVIS existantes
try:
    from memoire_thermique_turbo_adaptatif import get_memoire_thermique
    THERMAL_MEMORY_AVAILABLE = True
    print("✅ Intégration mémoire thermique JARVIS activée")
except ImportError:
    THERMAL_MEMORY_AVAILABLE = False
    print("⚠️ Mémoire thermique JARVIS non disponible")

try:
    from jarvis_mcp_protocol import send_mcp_message
    MCP_AVAILABLE = True
    print("✅ Intégration MCP Protocol activée")
except ImportError:
    MCP_AVAILABLE = False

# ============================================================================
# STRUCTURES DE DONNÉES COGNITIVES
# ============================================================================

@dataclass
class EpisodeMemoire:
    """Épisode de mémoire épisodique structuré"""
    id: str
    timestamp: datetime
    contexte: str
    action: str
    resultat: str
    emotions: Dict[str, float]
    importance: float
    concepts_lies: List[str]

    def to_dict(self) -> Dict[str, Any]:
        """Convertit l'épisode en dictionnaire pour sauvegarde"""
        return {
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "contexte": self.contexte,
            "action": self.action,
            "resultat": self.resultat,
            "emotions": self.emotions,
            "importance": self.importance,
            "concepts_lies": self.concepts_lies
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EpisodeMemoire':
        """Crée un épisode depuis un dictionnaire"""
        return cls(
            id=data["id"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            contexte=data["contexte"],
            action=data["action"],
            resultat=data["resultat"],
            emotions=data.get("emotions", {}),
            importance=data.get("importance", 1.0),
            concepts_lies=data.get("concepts_lies", [])
        )

@dataclass
class PlanRaisonnement:
    """Plan de raisonnement structuré"""
    objectif: str
    etapes: List[str]
    concepts_utilises: List[str]
    confiance: float
    duree_estimee: float
    created_at: datetime

    def to_dict(self) -> Dict[str, Any]:
        """Convertit le plan en dictionnaire"""
        return {
            "objectif": self.objectif,
            "etapes": self.etapes,
            "concepts_utilises": self.concepts_utilises,
            "confiance": self.confiance,
            "duree_estimee": self.duree_estimee,
            "created_at": self.created_at.isoformat()
        }

# ============================================================================
# MOTEUR DE RAISONNEMENT COGNITIF PRINCIPAL
# ============================================================================

class RaisonnementCognitif:
    """Moteur de raisonnement cognitif avancé pour JARVIS"""

    def __init__(self, memoire_thermique_path="thermal_memory_persistent.json"):
        self.memoire_path = memoire_thermique_path
        self.episodes_memoire = []
        self.graphe_semantique = nx.Graph()
        self.plans_raisonnement = []

        # Configuration optimisée selon le matériel
        if APPLE_SILICON_OPTIMIZATIONS:
            self.max_episodes_memoire = 10000 if MEMORY_OPTIMIZATION_LEVEL == "ultra" else 5000
            self.max_concepts_graphe = 5000 if MEMORY_OPTIMIZATION_LEVEL == "ultra" else 2000
            self.parallel_processing = True
            print(f"🧠 Configuration cognitive optimisée: {MEMORY_OPTIMIZATION_LEVEL}")
        else:
            self.max_episodes_memoire = 1000
            self.max_concepts_graphe = 500
            self.parallel_processing = False

        # Initialisation
        self.charger_memoire_episodes()
        self.charger_graphe_semantique()
        self.integrer_memoire_thermique()

        print("🧠 JARVIS Raisonnement Cognitif initialisé")
        print(f"   📚 Épisodes mémoire: {len(self.episodes_memoire)}")
        print(f"   🕸️ Concepts graphe: {len(self.graphe_semantique.nodes)}")
        print(f"   🔗 Relations: {len(self.graphe_semantique.edges)}")

    def charger_memoire_episodes(self):
        """Charge les épisodes de mémoire depuis le fichier"""
        try:
            episodes_file = "jarvis_episodes_memoire.json"
            with open(episodes_file, "r", encoding="utf-8") as f:
                data = json.load(f)
                self.episodes_memoire = [
                    EpisodeMemoire.from_dict(episode_data)
                    for episode_data in data.get("episodes", [])
                ]
            print(f"✅ {len(self.episodes_memoire)} épisodes de mémoire chargés")
        except FileNotFoundError:
            self.episodes_memoire = []
            print("📝 Nouvelle base d'épisodes de mémoire créée")
        except Exception as e:
            print(f"⚠️ Erreur chargement épisodes: {e}")
            self.episodes_memoire = []

    def sauvegarder_episodes_memoire(self):
        """Sauvegarde les épisodes de mémoire"""
        try:
            episodes_file = "jarvis_episodes_memoire.json"
            data = {
                "episodes": [episode.to_dict() for episode in self.episodes_memoire],
                "last_updated": datetime.now().isoformat(),
                "total_episodes": len(self.episodes_memoire)
            }
            with open(episodes_file, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"💾 {len(self.episodes_memoire)} épisodes sauvegardés")
        except Exception as e:
            print(f"❌ Erreur sauvegarde épisodes: {e}")

    def charger_graphe_semantique(self):
        """Charge et initialise le graphe sémantique"""
        try:
            graphe_file = "jarvis_graphe_semantique.json"
            with open(graphe_file, "r", encoding="utf-8") as f:
                data = json.load(f)

                # Reconstruire le graphe
                self.graphe_semantique = nx.Graph()
                self.graphe_semantique.add_nodes_from(data.get("nodes", []))
                self.graphe_semantique.add_edges_from(data.get("edges", []))

            print(f"✅ Graphe sémantique chargé: {len(self.graphe_semantique.nodes)} concepts")
        except FileNotFoundError:
            # Initialiser avec des concepts de base
            self._initialiser_graphe_base()
            print("🕸️ Nouveau graphe sémantique initialisé")
        except Exception as e:
            print(f"⚠️ Erreur chargement graphe: {e}")
            self._initialiser_graphe_base()

    def _initialiser_graphe_base(self):
        """Initialise le graphe avec des concepts de base"""
        concepts_base = [
            "mémoire", "raisonnement", "objectif", "erreur", "succès",
            "apprentissage", "créativité", "logique", "émotion", "contexte",
            "action", "résultat", "plan", "stratégie", "problème", "solution",
            "intelligence", "connaissance", "expérience", "intuition"
        ]

        relations_base = [
            ("mémoire", "raisonnement"), ("raisonnement", "objectif"),
            ("objectif", "plan"), ("plan", "action"), ("action", "résultat"),
            ("erreur", "apprentissage"), ("apprentissage", "connaissance"),
            ("créativité", "solution"), ("logique", "raisonnement"),
            ("émotion", "contexte"), ("contexte", "action"),
            ("expérience", "mémoire"), ("intuition", "créativité"),
            ("problème", "solution"), ("stratégie", "plan"),
            ("intelligence", "raisonnement"), ("connaissance", "mémoire")
        ]

        self.graphe_semantique.add_nodes_from(concepts_base)
        self.graphe_semantique.add_edges_from(relations_base)

    def sauvegarder_graphe_semantique(self):
        """Sauvegarde le graphe sémantique"""
        try:
            graphe_file = "jarvis_graphe_semantique.json"
            data = {
                "nodes": list(self.graphe_semantique.nodes),
                "edges": list(self.graphe_semantique.edges),
                "last_updated": datetime.now().isoformat(),
                "total_nodes": len(self.graphe_semantique.nodes),
                "total_edges": len(self.graphe_semantique.edges)
            }
            with open(graphe_file, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"💾 Graphe sémantique sauvegardé: {len(self.graphe_semantique.nodes)} concepts")
        except Exception as e:
            print(f"❌ Erreur sauvegarde graphe: {e}")

    def integrer_memoire_thermique(self):
        """Intègre la mémoire thermique JARVIS existante"""
        if not THERMAL_MEMORY_AVAILABLE:
            return

        try:
            memoire_thermique = get_memoire_thermique()
            episodes_integres = 0

            for souvenir in memoire_thermique[-100:]:  # 100 derniers souvenirs
                if isinstance(souvenir, dict):
                    # Convertir souvenir thermique en épisode cognitif
                    episode = EpisodeMemoire(
                        id=str(uuid.uuid4()),
                        timestamp=datetime.now(),
                        contexte=souvenir.get("user_message", "")[:200],
                        action="interaction_jarvis",
                        resultat=souvenir.get("agent_response", "")[:200],
                        emotions={"satisfaction": 0.7, "curiosité": 0.5},
                        importance=1.0,
                        concepts_lies=self._extraire_concepts(
                            souvenir.get("user_message", "") + " " +
                            souvenir.get("agent_response", "")
                        )
                    )

                    self.episodes_memoire.append(episode)
                    episodes_integres += 1

            print(f"🔗 {episodes_integres} souvenirs thermiques intégrés en épisodes cognitifs")

        except Exception as e:
            print(f"⚠️ Erreur intégration mémoire thermique: {e}")

    def _extraire_concepts(self, texte: str) -> List[str]:
        """Extrait les concepts présents dans un texte"""
        concepts_trouves = []
        texte_lower = texte.lower()

        for concept in self.graphe_semantique.nodes:
            if concept.lower() in texte_lower:
                concepts_trouves.append(concept)

        return concepts_trouves[:5]  # Limiter à 5 concepts max

    def ajouter_episode(self, contexte: str, action: str, resultat: str,
                       emotions: Optional[Dict[str, float]] = None,
                       importance: float = 1.0) -> EpisodeMemoire:
        """Ajoute un nouvel épisode de mémoire"""

        if emotions is None:
            emotions = {"neutre": 0.5}

        # Extraire les concepts liés
        texte_complet = f"{contexte} {action} {resultat}"
        concepts_lies = self._extraire_concepts(texte_complet)

        # Créer l'épisode
        episode = EpisodeMemoire(
            id=str(uuid.uuid4()),
            timestamp=datetime.now(),
            contexte=contexte,
            action=action,
            resultat=resultat,
            emotions=emotions,
            importance=importance,
            concepts_lies=concepts_lies
        )

        # Ajouter à la mémoire
        self.episodes_memoire.append(episode)

        # Maintenir la limite de mémoire
        if len(self.episodes_memoire) > self.max_episodes_memoire:
            # Supprimer les épisodes les moins importants et les plus anciens
            self.episodes_memoire.sort(key=lambda e: (e.importance, e.timestamp))
            self.episodes_memoire = self.episodes_memoire[100:]  # Garder les plus récents/importants

        # Enrichir le graphe sémantique
        self._enrichir_graphe_avec_episode(episode)

        # Sauvegarder
        self.sauvegarder_episodes_memoire()

        # Notifier via MCP si disponible
        if MCP_AVAILABLE:
            try:
                send_mcp_message({
                    "type": "nouvel_episode_cognitif",
                    "episode_id": episode.id,
                    "contexte": contexte[:100],
                    "importance": importance,
                    "concepts": concepts_lies,
                    "timestamp": episode.timestamp.isoformat()
                })
            except Exception as e:
                print(f"⚠️ Erreur notification MCP: {e}")

        print(f"📚 Nouvel épisode ajouté: {episode.id[:8]}... (importance: {importance})")
        return episode

    def _enrichir_graphe_avec_episode(self, episode: EpisodeMemoire):
        """Enrichit le graphe sémantique avec un nouvel épisode"""
        # Ajouter les nouveaux concepts
        for concept in episode.concepts_lies:
            if concept not in self.graphe_semantique:
                self.ajouter_concept(concept)

        # Créer des relations entre concepts co-occurrents
        for i, concept1 in enumerate(episode.concepts_lies):
            for concept2 in episode.concepts_lies[i+1:]:
                if not self.graphe_semantique.has_edge(concept1, concept2):
                    self.relier_concepts(concept1, concept2, poids=episode.importance)

    def ajouter_concept(self, concept: str, attributs: Optional[Dict[str, Any]] = None):
        """Ajoute un concept au graphe sémantique"""
        if concept not in self.graphe_semantique:
            self.graphe_semantique.add_node(concept, **(attributs or {}))

            # Maintenir la limite du graphe
            if len(self.graphe_semantique.nodes) > self.max_concepts_graphe:
                # Supprimer les concepts les moins connectés
                degres = dict(self.graphe_semantique.degree())
                concepts_a_supprimer = sorted(degres.items(), key=lambda x: x[1])[:100]
                for concept_sup, _ in concepts_a_supprimer:
                    self.graphe_semantique.remove_node(concept_sup)

            print(f"🕸️ Nouveau concept ajouté: {concept}")

    def relier_concepts(self, concept1: str, concept2: str, poids: float = 1.0):
        """Relie deux concepts dans le graphe sémantique"""
        if concept1 in self.graphe_semantique and concept2 in self.graphe_semantique:
            if self.graphe_semantique.has_edge(concept1, concept2):
                # Renforcer la relation existante
                current_weight = self.graphe_semantique[concept1][concept2].get('weight', 1.0)
                new_weight = min(current_weight + poids * 0.1, 5.0)  # Max 5.0
                self.graphe_semantique[concept1][concept2]['weight'] = new_weight
            else:
                self.graphe_semantique.add_edge(concept1, concept2, weight=poids)
                print(f"🔗 Nouvelle relation: {concept1} ↔ {concept2} (poids: {poids})")

    def planifier_objectif(self, objectif: str, contexte: Optional[str] = None) -> PlanRaisonnement:
        """Planifie les étapes pour atteindre un objectif"""

        # Ajouter l'objectif au graphe s'il n'existe pas
        if objectif not in self.graphe_semantique:
            self.ajouter_concept(objectif)
            # Relier à des concepts pertinents
            concepts_pertinents = ["objectif", "plan", "action", "succès"]
            for concept in concepts_pertinents:
                if concept in self.graphe_semantique:
                    self.relier_concepts(objectif, concept)

        # Rechercher des chemins vers le succès
        chemins_possibles = []
        concepts_cibles = ["succès", "résultat", "solution", "accomplissement"]

        for cible in concepts_cibles:
            if cible in self.graphe_semantique:
                try:
                    if APPLE_SILICON_OPTIMIZATIONS and self.parallel_processing:
                        # Utiliser l'algorithme optimisé pour Apple Silicon
                        chemin = nx.shortest_path(
                            self.graphe_semantique,
                            source=objectif,
                            target=cible,
                            weight='weight'
                        )
                    else:
                        chemin = nx.shortest_path(
                            self.graphe_semantique,
                            source=objectif,
                            target=cible
                        )
                    chemins_possibles.append(chemin)
                except nx.NetworkXNoPath:
                    continue

        # Sélectionner le meilleur chemin
        if chemins_possibles:
            # Prendre le chemin le plus court
            meilleur_chemin = min(chemins_possibles, key=len)
            confiance = max(0.3, 1.0 - (len(meilleur_chemin) - 2) * 0.1)
        else:
            # Plan générique si aucun chemin trouvé
            meilleur_chemin = [objectif, "analyse", "planification", "action", "évaluation", "succès"]
            confiance = 0.2

        # Enrichir le plan avec l'expérience passée
        etapes_enrichies = self._enrichir_plan_avec_experience(meilleur_chemin, contexte)

        # Créer le plan de raisonnement
        plan = PlanRaisonnement(
            objectif=objectif,
            etapes=etapes_enrichies,
            concepts_utilises=meilleur_chemin,
            confiance=confiance,
            duree_estimee=len(etapes_enrichies) * 2.0,  # 2 minutes par étape
            created_at=datetime.now()
        )

        self.plans_raisonnement.append(plan)

        print(f"📋 Plan créé pour '{objectif}': {len(etapes_enrichies)} étapes (confiance: {confiance:.2f})")
        return plan

    def _enrichir_plan_avec_experience(self, chemin_base: List[str], contexte: Optional[str]) -> List[str]:
        """Enrichit un plan avec l'expérience des épisodes passés"""
        etapes_enrichies = []

        for i, concept in enumerate(chemin_base):
            # Étape de base
            if i == 0:
                etapes_enrichies.append(f"Définir clairement l'objectif: {concept}")
            elif i == len(chemin_base) - 1:
                etapes_enrichies.append(f"Atteindre le résultat: {concept}")
            else:
                etapes_enrichies.append(f"Étape {i}: {concept}")

            # Ajouter des conseils basés sur l'expérience
            conseils_experience = self._rechercher_conseils_experience(concept, contexte)
            if conseils_experience:
                etapes_enrichies.append(f"  💡 Conseil: {conseils_experience}")

        return etapes_enrichies

    def _rechercher_conseils_experience(self, concept: str, contexte: Optional[str]) -> Optional[str]:
        """Recherche des conseils basés sur l'expérience passée"""
        episodes_pertinents = []

        for episode in self.episodes_memoire:
            if concept in episode.concepts_lies:
                # Calculer la pertinence
                pertinence = episode.importance
                if contexte and contexte.lower() in episode.contexte.lower():
                    pertinence += 0.5

                episodes_pertinents.append((episode, pertinence))

        if episodes_pertinents:
            # Prendre l'épisode le plus pertinent
            meilleur_episode, _ = max(episodes_pertinents, key=lambda x: x[1])

            # Extraire un conseil
            if "succès" in meilleur_episode.resultat.lower():
                return f"Basé sur l'expérience: {meilleur_episode.action[:100]}..."
            elif "erreur" in meilleur_episode.resultat.lower():
                return f"Éviter: {meilleur_episode.action[:100]}..."

        return None

    def retrospection(self, n: int = 5, filtre_importance: Optional[float] = None) -> List[EpisodeMemoire]:
        """Effectue une rétrospection sur les derniers épisodes"""
        episodes_filtres = self.episodes_memoire

        if filtre_importance is not None:
            episodes_filtres = [e for e in episodes_filtres if e.importance >= filtre_importance]

        # Trier par timestamp décroissant
        episodes_filtres.sort(key=lambda e: e.timestamp, reverse=True)

        return episodes_filtres[:n]

    def analyser_patterns_cognitifs(self) -> Dict[str, Any]:
        """Analyse les patterns cognitifs dans la mémoire épisodique"""
        if not self.episodes_memoire:
            return {"message": "Aucun épisode à analyser"}

        # Analyse des concepts les plus fréquents
        concepts_frequence = {}
        for episode in self.episodes_memoire:
            for concept in episode.concepts_lies:
                concepts_frequence[concept] = concepts_frequence.get(concept, 0) + 1

        # Analyse des émotions dominantes
        emotions_moyennes = {}
        for episode in self.episodes_memoire:
            for emotion, valeur in episode.emotions.items():
                if emotion not in emotions_moyennes:
                    emotions_moyennes[emotion] = []
                emotions_moyennes[emotion].append(valeur)

        for emotion in emotions_moyennes:
            emotions_moyennes[emotion] = sum(emotions_moyennes[emotion]) / len(emotions_moyennes[emotion])

        # Analyse temporelle
        episodes_par_jour = {}
        for episode in self.episodes_memoire:
            jour = episode.timestamp.date().isoformat()
            episodes_par_jour[jour] = episodes_par_jour.get(jour, 0) + 1

        return {
            "total_episodes": len(self.episodes_memoire),
            "concepts_frequents": sorted(concepts_frequence.items(), key=lambda x: x[1], reverse=True)[:10],
            "emotions_dominantes": sorted(emotions_moyennes.items(), key=lambda x: x[1], reverse=True),
            "activite_par_jour": episodes_par_jour,
            "graphe_stats": {
                "concepts_total": len(self.graphe_semantique.nodes),
                "relations_total": len(self.graphe_semantique.edges),
                "concepts_centraux": self._identifier_concepts_centraux()
            }
        }

    def _identifier_concepts_centraux(self) -> List[Tuple[str, float]]:
        """Identifie les concepts les plus centraux dans le graphe"""
        if not self.graphe_semantique.nodes:
            return []

        centralite = nx.degree_centrality(self.graphe_semantique)
        return sorted(centralite.items(), key=lambda x: x[1], reverse=True)[:5]

    def get_status_cognitif(self) -> Dict[str, Any]:
        """Retourne le statut complet du système cognitif"""
        return {
            "episodes_memoire": len(self.episodes_memoire),
            "concepts_graphe": len(self.graphe_semantique.nodes),
            "relations_graphe": len(self.graphe_semantique.edges),
            "plans_crees": len(self.plans_raisonnement),
            "optimisations_actives": {
                "apple_silicon": APPLE_SILICON_OPTIMIZATIONS,
                "niveau_optimisation": MEMORY_OPTIMIZATION_LEVEL,
                "processing_parallele": getattr(self, 'parallel_processing', False)
            },
            "integrations": {
                "memoire_thermique": THERMAL_MEMORY_AVAILABLE,
                "mcp_protocol": MCP_AVAILABLE
            },
            "derniere_activite": self.episodes_memoire[-1].timestamp.isoformat() if self.episodes_memoire else None
        }

    def sauvegarder_tout(self):
        """Sauvegarde complète du système cognitif"""
        self.sauvegarder_episodes_memoire()
        self.sauvegarder_graphe_semantique()
        print("💾 Sauvegarde complète du système cognitif effectuée")

# ============================================================================
# INSTANCE GLOBALE JARVIS RAISONNEMENT COGNITIF
# ============================================================================

# Instance globale pour intégration avec JARVIS
JARVIS_RAISONNEMENT = None

def get_jarvis_raisonnement() -> RaisonnementCognitif:
    """Retourne l'instance de raisonnement cognitif de JARVIS"""
    global JARVIS_RAISONNEMENT
    if JARVIS_RAISONNEMENT is None:
        JARVIS_RAISONNEMENT = RaisonnementCognitif()
    return JARVIS_RAISONNEMENT

def ajouter_episode_cognitif(contexte: str, action: str, resultat: str,
                           emotions: Optional[Dict[str, float]] = None,
                           importance: float = 1.0) -> str:
    """Interface simple pour ajouter un épisode cognitif"""
    raisonnement = get_jarvis_raisonnement()
    episode = raisonnement.ajouter_episode(contexte, action, resultat, emotions, importance)
    return episode.id

def planifier_objectif_cognitif(objectif: str, contexte: Optional[str] = None) -> Dict[str, Any]:
    """Interface simple pour planifier un objectif"""
    raisonnement = get_jarvis_raisonnement()
    plan = raisonnement.planifier_objectif(objectif, contexte)
    return plan.to_dict()

def analyser_patterns() -> Dict[str, Any]:
    """Interface simple pour analyser les patterns cognitifs"""
    raisonnement = get_jarvis_raisonnement()
    return raisonnement.analyser_patterns_cognitifs()

def get_status_raisonnement() -> Dict[str, Any]:
    """Interface simple pour obtenir le statut du raisonnement"""
    raisonnement = get_jarvis_raisonnement()
    return raisonnement.get_status_cognitif()

def retrospection_cognitive(n: int = 5) -> List[Dict[str, Any]]:
    """Interface simple pour la rétrospection"""
    raisonnement = get_jarvis_raisonnement()
    episodes = raisonnement.retrospection(n)
    return [episode.to_dict() for episode in episodes]

# ============================================================================
# INTÉGRATION AVEC GOAP (si disponible)
# ============================================================================

def integrer_avec_goap():
    """Intègre le raisonnement cognitif avec le système GOAP"""
    try:
        from jarvis_goap_planner import get_jarvis_goap
        goap = get_jarvis_goap()
        raisonnement = get_jarvis_raisonnement()

        # Créer des actions GOAP basées sur le raisonnement cognitif
        from jarvis_goap_planner import GOAPAction

        action_raisonnement = GOAPAction(
            name="raisonnement_cognitif",
            preconditions={"probleme_complexe": True, "contexte_charge": True},
            effects={"plan_raisonne": True, "solution_structuree": True},
            cost=3.0,
            duration=5.0,
            category="cognitive",
            description="Utiliser le raisonnement cognitif pour résoudre un problème complexe"
        )

        goap.add_custom_action(action_raisonnement)
        print("🔗 Intégration GOAP ↔ Raisonnement Cognitif réussie")

    except ImportError:
        print("⚠️ Module GOAP non disponible pour l'intégration")
    except Exception as e:
        print(f"❌ Erreur intégration GOAP: {e}")

# ============================================================================
# TESTS ET DÉMONSTRATION
# ============================================================================

if __name__ == "__main__":
    print("🧪 Test JARVIS Raisonnement Cognitif")
    print("=" * 50)

    # Initialiser le système
    rc = get_jarvis_raisonnement()

    # Test 1: Ajouter des épisodes
    print("\n📚 Test 1: Ajout d'épisodes")
    episode1_id = ajouter_episode_cognitif(
        contexte="Utilisateur demande optimisation performance",
        action="Analyse système + nettoyage cache",
        resultat="Amélioration 25% performance",
        emotions={"satisfaction": 0.8, "fierté": 0.6},
        importance=2.0
    )

    episode2_id = ajouter_episode_cognitif(
        contexte="Problème complexe de raisonnement",
        action="Application méthode structurée",
        resultat="Solution trouvée en 3 étapes",
        emotions={"accomplissement": 0.9, "confiance": 0.7},
        importance=2.5
    )

    # Test 2: Planification d'objectif
    print("\n🎯 Test 2: Planification d'objectif")
    plan = planifier_objectif_cognitif(
        "améliorer intelligence artificielle",
        contexte="système JARVIS existant"
    )
    print(f"Plan créé: {len(plan['etapes'])} étapes")
    for i, etape in enumerate(plan['etapes'][:3]):  # Afficher 3 premières étapes
        print(f"  {i+1}. {etape}")

    # Test 3: Analyse des patterns
    print("\n🔍 Test 3: Analyse des patterns cognitifs")
    patterns = analyser_patterns()
    print(f"Total épisodes: {patterns['total_episodes']}")
    print(f"Concepts fréquents: {patterns['concepts_frequents'][:3]}")
    print(f"Émotions dominantes: {patterns['emotions_dominantes'][:3]}")

    # Test 4: Rétrospection
    print("\n🔄 Test 4: Rétrospection")
    souvenirs = retrospection_cognitive(3)
    print(f"Derniers {len(souvenirs)} épisodes:")
    for souvenir in souvenirs:
        print(f"  - {souvenir['contexte'][:50]}... → {souvenir['resultat'][:50]}...")

    # Test 5: Statut système
    print("\n📊 Test 5: Statut du système")
    status = get_status_raisonnement()
    print(f"Épisodes: {status['episodes_memoire']}")
    print(f"Concepts: {status['concepts_graphe']}")
    print(f"Relations: {status['relations_graphe']}")
    print(f"Optimisations Apple Silicon: {status['optimisations_actives']['apple_silicon']}")

    # Test 6: Intégration GOAP
    print("\n🔗 Test 6: Intégration GOAP")
    integrer_avec_goap()

    print("\n🎉 Tests terminés avec succès!")
    print("Le module de raisonnement cognitif est prêt pour l'intégration dans JARVIS.")

    # Sauvegarde finale
    rc.sauvegarder_tout()