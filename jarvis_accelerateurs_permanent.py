#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 JARVIS ACCÉLÉRATEURS PERMANENTS
==================================
Système de surveillance et maintien des accélérateurs JARVIS
Empêche la déconnexion automatique des turbo accélérateurs
"""

import json
import time
import threading
import requests
import subprocess
import os
from datetime import datetime

# Configuration des accélérateurs
ACCELERATEURS_CONFIG = {
    "turbo_cascade_illimite": {
        "active": True,
        "facteur": 100,
        "priorite": 1,
        "description": "Turbo cascade illimité - Performance maximale"
    },
    "apple_silicon_m4": {
        "active": True,
        "facteur": 50,
        "priorite": 2,
        "description": "Optimisation Apple Silicon M4"
    },
    "neural_engine": {
        "active": True,
        "facteur": 75,
        "priorite": 3,
        "description": "Neural Engine Apple"
    },
    "memory_turbo": {
        "active": True,
        "facteur": 60,
        "priorite": 4,
        "description": "Turbo mémoire thermique"
    },
    "deepseek_optimization": {
        "active": True,
        "facteur": 80,
        "priorite": 5,
        "description": "Optimisation DeepSeek R1 8B"
    }
}

# Paramètres de surveillance
SURVEILLANCE_ACTIVE = True
CHECK_INTERVAL = 30  # Vérification toutes les 30 secondes
PERFORMANCE_THRESHOLD = 3.0  # Seuil de performance en secondes

class JarvisAccelerateursManager:
    def __init__(self):
        self.surveillance_thread = None
        self.last_check = datetime.now()
        self.performance_history = []
        
    def start_surveillance(self):
        """Démarre la surveillance permanente des accélérateurs"""
        if self.surveillance_thread and self.surveillance_thread.is_alive():
            print("⚠️ Surveillance déjà active")
            return
            
        self.surveillance_thread = threading.Thread(target=self._surveillance_loop, daemon=True)
        self.surveillance_thread.start()
        print("🚀 Surveillance accélérateurs démarrée")
        
    def _surveillance_loop(self):
        """Boucle de surveillance continue"""
        while SURVEILLANCE_ACTIVE:
            try:
                # Vérifier l'état des accélérateurs
                self._check_accelerateurs_status()
                
                # Tester la performance
                performance = self._test_performance()
                self.performance_history.append(performance)
                
                # Garder seulement les 10 derniers tests
                if len(self.performance_history) > 10:
                    self.performance_history.pop(0)
                
                # Réactiver si nécessaire
                if performance > PERFORMANCE_THRESHOLD:
                    print(f"🐌 Performance dégradée: {performance:.2f}s")
                    self._force_reactivation()
                else:
                    print(f"✅ Performance OK: {performance:.2f}s")
                
                # Sauvegarder l'état
                self._save_accelerateurs_config()
                
                time.sleep(CHECK_INTERVAL)
                
            except Exception as e:
                print(f"❌ Erreur surveillance: {e}")
                time.sleep(60)  # Attendre plus longtemps en cas d'erreur
                
    def _check_accelerateurs_status(self):
        """Vérifie l'état des fichiers de configuration"""
        config_files = [
            "jarvis_accelerateurs_persistance.json",
            "turbo_config.json"
        ]
        
        for file in config_files:
            if not os.path.exists(file):
                print(f"❌ Fichier manquant: {file}")
                self._recreate_config_file(file)
            else:
                # Vérifier le contenu
                try:
                    with open(file, 'r') as f:
                        data = json.load(f)
                        if not data:
                            print(f"⚠️ Fichier vide: {file}")
                            self._recreate_config_file(file)
                except:
                    print(f"❌ Fichier corrompu: {file}")
                    self._recreate_config_file(file)
                    
    def _recreate_config_file(self, filename):
        """Recrée un fichier de configuration manquant"""
        if "accelerateurs" in filename:
            with open(filename, 'w') as f:
                json.dump(ACCELERATEURS_CONFIG, f, indent=2)
        elif "turbo" in filename:
            turbo_config = {
                "turbo_level": 1.0,
                "cpu_optimization": True,
                "memory_optimization": True,
                "neural_acceleration": True,
                "apple_silicon_boost": True,
                "deepseek_turbo": True
            }
            with open(filename, 'w') as f:
                json.dump(turbo_config, f, indent=2)
        
        print(f"✅ Fichier recréé: {filename}")
        
    def _test_performance(self):
        """Teste la performance de DeepSeek"""
        try:
            start_time = time.time()
            
            response = requests.post(
                'http://localhost:8000/v1/chat/completions',
                json={
                    'model': 'DeepSeek R1 0528 Qwen3 8B',
                    'messages': [{'role': 'user', 'content': 'Test vitesse'}],
                    'max_tokens': 10,
                    'temperature': 0.1
                },
                timeout=10
            )
            
            end_time = time.time()
            
            if response.status_code == 200:
                return end_time - start_time
            else:
                return 999  # Erreur = performance très mauvaise
                
        except Exception as e:
            print(f"⚠️ Erreur test performance: {e}")
            return 999
            
    def _force_reactivation(self):
        """Force la réactivation des accélérateurs"""
        print("🚀 RÉACTIVATION FORCÉE DES ACCÉLÉRATEURS")
        
        # Recréer tous les fichiers de config
        self._save_accelerateurs_config()
        
        # Forcer l'optimisation Apple Silicon
        self._optimize_apple_silicon()
        
        print("⚡ Accélérateurs réactivés à 100%")
        
    def _optimize_apple_silicon(self):
        """Optimise spécifiquement pour Apple Silicon"""
        try:
            # Vérifier l'architecture
            result = subprocess.run(['uname', '-m'], capture_output=True, text=True)
            if 'arm64' in result.stdout:
                print("🍎 Optimisation Apple Silicon M4 activée")
                
                # Optimisations spécifiques
                os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'
                os.environ['PYTORCH_MPS_HIGH_WATERMARK_RATIO'] = '0.0'
                
        except Exception as e:
            print(f"⚠️ Erreur optimisation Apple Silicon: {e}")
            
    def _save_accelerateurs_config(self):
        """Sauvegarde la configuration des accélérateurs"""
        try:
            with open("jarvis_accelerateurs_persistance.json", 'w') as f:
                json.dump(ACCELERATEURS_CONFIG, f, indent=2)
                
            turbo_config = {
                "turbo_level": 1.0,
                "cpu_optimization": True,
                "memory_optimization": True,
                "neural_acceleration": True,
                "apple_silicon_boost": True,
                "deepseek_turbo": True,
                "last_update": datetime.now().isoformat()
            }
            
            with open("turbo_config.json", 'w') as f:
                json.dump(turbo_config, f, indent=2)
                
        except Exception as e:
            print(f"❌ Erreur sauvegarde config: {e}")
            
    def get_status(self):
        """Retourne le statut des accélérateurs"""
        avg_performance = sum(self.performance_history) / len(self.performance_history) if self.performance_history else 0
        
        return {
            "surveillance_active": SURVEILLANCE_ACTIVE,
            "last_check": self.last_check.isoformat(),
            "average_performance": avg_performance,
            "accelerateurs_count": len(ACCELERATEURS_CONFIG),
            "performance_history": self.performance_history[-5:]  # 5 derniers tests
        }

# Instance globale
accelerateurs_manager = JarvisAccelerateursManager()

def start_permanent_accelerators():
    """Démarre les accélérateurs permanents"""
    print("🚀 DÉMARRAGE ACCÉLÉRATEURS PERMANENTS JARVIS")
    accelerateurs_manager.start_surveillance()
    return "✅ Accélérateurs permanents activés"

def get_accelerators_status():
    """Retourne le statut des accélérateurs"""
    return accelerateurs_manager.get_status()

if __name__ == "__main__":
    print("🚀 JARVIS ACCÉLÉRATEURS PERMANENTS")
    print("=" * 50)
    
    # Démarrer la surveillance
    start_permanent_accelerators()
    
    try:
        # Garder le script actif
        while True:
            time.sleep(60)
            status = get_accelerators_status()
            print(f"📊 Performance moyenne: {status['average_performance']:.2f}s")
            
    except KeyboardInterrupt:
        print("\n🛑 Arrêt des accélérateurs permanents")
        SURVEILLANCE_ACTIVE = False
